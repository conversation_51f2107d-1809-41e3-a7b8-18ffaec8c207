# backend/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
import pandas as pd
import math
import json
import os
from datetime import datetime

app = FastAPI()

# Allow frontend to access the API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Path to your Excel file
# EXCEL_FILE = r"C:\Users\<USER>\Desktop\Get_Courses\Udemy-courses.xlsx"
EXCEL_FILE = r"C:\Users\<USER>\Desktop\Display_courses\Get_Courses\Udemy-courses.xlsx"

def read_courses():
    df = pd.read_excel(EXCEL_FILE)
    # Replace NaN with None to avoid JSON errors
    df = df.where(pd.notnull(df), None)
    # Convert dataframe to list of dicts
    courses = df.to_dict(orient="records")
    return courses

@app.get("/courses")
def get_courses():
    return read_courses()

@app.get("/export/excel")
def export_excel():
    """Export courses to Excel file"""
    try:
        courses = read_courses()
        df = pd.DataFrame(courses)

        # Create exports directory if it doesn't exist
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"udemy_courses_export_{timestamp}.xlsx"
        filepath = os.path.join(export_dir, filename)

        # Save to Excel
        df.to_excel(filepath, index=False)

        return FileResponse(
            path=filepath,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        return {"error": f"Export failed: {str(e)}"}

@app.get("/export/csv")
def export_csv():
    """Export courses to CSV file"""
    try:
        courses = read_courses()
        df = pd.DataFrame(courses)

        # Create exports directory if it doesn't exist
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"udemy_courses_export_{timestamp}.csv"
        filepath = os.path.join(export_dir, filename)

        # Save to CSV
        df.to_csv(filepath, index=False)

        return FileResponse(
            path=filepath,
            filename=filename,
            media_type="text/csv"
        )
    except Exception as e:
        return {"error": f"Export failed: {str(e)}"}

@app.get("/export/json")
def export_json():
    """Export courses to JSON file"""
    try:
        courses = read_courses()

        # Create exports directory if it doesn't exist
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"udemy_courses_export_{timestamp}.json"
        filepath = os.path.join(export_dir, filename)

        # Save to JSON
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(courses, f, indent=2, ensure_ascii=False)

        return FileResponse(
            path=filepath,
            filename=filename,
            media_type="application/json"
        )
    except Exception as e:
        return {"error": f"Export failed: {str(e)}"}
