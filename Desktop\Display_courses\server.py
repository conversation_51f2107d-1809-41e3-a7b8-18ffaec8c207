# backend/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import pandas as pd
import math

app = FastAPI()

# Allow frontend to access the API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Path to your Excel file
# EXCEL_FILE = r"C:\Users\<USER>\Desktop\Get_Courses\Udemy-courses.xlsx"
EXCEL_FILE = r"C:\Users\<USER>\Desktop\Display_courses\Get_Courses\Udemy-courses.xlsx"

def read_courses():
    df = pd.read_excel(EXCEL_FILE)
    # Replace NaN with None to avoid JSON errors
    df = df.where(pd.notnull(df), None)
    # Convert dataframe to list of dicts
    courses = df.to_dict(orient="records")
    return courses

@app.get("/courses")
def get_courses():
    return read_courses()
