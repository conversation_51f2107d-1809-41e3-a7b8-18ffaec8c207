import React, { useEffect, useState } from "react";

function App() {
  const [courses, setCourses] = useState([]);
  const [search, setSearch] = useState("");
  const [sortField, setSortField] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");

  useEffect(() => {
    fetch("http://127.0.0.1:8000/courses")
      .then((res) => res.json())
      .then((data) => setCourses(data))
      .catch((err) => console.error(err));
  }, []);

  const handleSort = (field) => {
    const order = sortField === field && sortOrder === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortOrder(order);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "-"; // invalid date handling

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // months 0-11
    const year = String(date.getFullYear()).slice(-2);
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const filteredCourses = courses
    .filter(
      (c) =>
        c.Title.toLowerCase().includes(search.toLowerCase()) ||
        (c.Instructors &&
          c.Instructors.toLowerCase().includes(search.toLowerCase()))
    )
    .sort((a, b) => {
      if (!sortField) return 0;
      const valA = a[sortField] ?? 0;
      const valB = b[sortField] ?? 0;
      if (typeof valA === "string") {
        return sortOrder === "asc"
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }
      return sortOrder === "asc" ? valA - valB : valB - valA;
    });

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-4 text-gray-800">Udemy Courses</h1>

        <div className="mb-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <input
            type="text"
            placeholder="Search by title or instructor..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="px-4 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-400"
          />
        </div>

        <div className="overflow-x-auto rounded-lg shadow-lg bg-white">
          <table className="min-w-full table-auto border-collapse">
            <thead className="bg-gray-100 text-gray-700 font-semibold">
              <tr>
                {[
                  "Id",
                  "Title",
                  "Completion ratio",
                  "Last accessed time",
                  "Instructors", // will not be sortable
                  "Star Rating",
                  "Number of Students",
                  "Course Length",
                ].map((field) =>
                  field === "Instructors" ? (
                    <th key={field} className="px-4 py-2 border">
                      {field}
                    </th>
                  ) : (
                    <th
                      key={field}
                      onClick={() => handleSort(field)}
                      className="px-4 py-2 border cursor-pointer hover:bg-gray-200 select-none"
                    >
                      {field}{" "}
                      {sortField === field
                        ? sortOrder === "asc"
                          ? "↑"
                          : "↓"
                        : ""}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {filteredCourses.map((c) => (
                <tr key={c.Id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 border">{c.Id}</td>
                  <td className="px-4 py-2 border">
                    <a
                      href={c.Url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {c.Title}
                    </a>
                  </td>
                  <td className="px-4 py-2 border">
                    {c["Completion ratio"] ?? 0}%
                  </td>
                  <td className="px-4 py-2 border">
                    {formatDate(c["Last accessed time"])}
                  </td>
                  <td className="px-4 py-2 border">{c.Instructors ?? "-"}</td>
                  <td className="px-4 py-2 border flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-[12px] h-[12px]"
                      viewBox="0 0 20 20"
                      fill="gold"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.957a1 1 0 00.95.69h4.153c.969 0 1.371 1.24.588 1.81l-3.36 2.44a1 1 0 00-.364 1.118l1.286 3.957c.3.921-.755 1.688-1.54 1.118l-3.36-2.44a1 1 0 00-1.175 0l-3.36 2.44c-.784.57-1.838-.197-1.539-1.118l1.285-3.957a1 1 0 00-.364-1.118L2.972 9.384c-.783-.57-.38-1.81.588-1.81h4.153a1 1 0 00.95-.69l1.286-3.957z" />
                    </svg>
                    <span className="font-semibold text-gray-800 text-sm">
                      {c["Star Rating"] ? c["Star Rating"].toFixed(1) : "-"}
                    </span>
                  </td>
                  <td className="px-4 py-2 border">
                    {c["Number of Students"] ?? "-"}
                  </td>
                  <td className="px-4 py-2 border">{c["Course Length"] ?? "-"}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredCourses.length === 0 && (
            <div className="p-4 text-center text-gray-500">No courses found.</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
