import React, { useEffect, useState } from "react";

function App() {
  const [courses, setCourses] = useState([]);
  const [search, setSearch] = useState("");
  const [sortField, setSortField] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");
  const [isLoading, setIsLoading] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);

  const fetchCourses = () => {
    setIsLoading(true);
    fetch("http://127.0.0.1:8000/courses")
      .then((res) => res.json())
      .then((data) => setCourses(data))
      .catch((err) => console.error(err))
      .finally(() => setIsLoading(false));
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportMenu && !event.target.closest('.export-dropdown')) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportMenu]);

  const handleSort = (field) => {
    const order = sortField === field && sortOrder === "asc" ? "desc" : "asc";
    setSortField(field);
    setSortOrder(order);
  };

  const handleRefresh = () => {
    fetchCourses();
  };

  const handleExport = async (format) => {
    try {
      setShowExportMenu(false);

      // Send the currently filtered courses to the backend
      const response = await fetch(`http://127.0.0.1:8000/export/${format}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          courses: filteredCourses,
          searchTerm: search.trim()
        })
      });

      if (response.ok) {
        // Create a blob from the response
        const blob = await response.blob();

        // Get filename from response headers or create default
        const contentDisposition = response.headers.get('content-disposition');

        // Map format to correct file extension
        const extensionMap = {
          'excel': 'xlsx',
          'csv': 'csv',
          'json': 'json'
        };
        const fileExtension = extensionMap[format] || format;

        // Create filename based on search term
        const createFilename = () => {
          const now = new Date();
          const dateStr = `${String(now.getDate()).padStart(2, '0')}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getFullYear()).slice(-2)}`;

          if (search.trim()) {
            // Clean search term for filename (replace spaces with hyphens, remove special chars)
            const cleanSearchTerm = search.trim()
              .replace(/\s+/g, '-')
              .replace(/[^a-zA-Z0-9\-]/g, '')
              .replace(/-+/g, '-')
              .replace(/^-|-$/g, '');

            return `${cleanSearchTerm}--${dateStr}.${fileExtension}`;
          } else {
            return `All-Courses--${dateStr}.${fileExtension}`;
          }
        };

        let filename = createFilename();

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        const errorData = await response.json();
        console.error('Export failed:', errorData.error);
        alert('Export failed: ' + errorData.error);
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('Export failed: ' + error.message);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "-"; // invalid date handling

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // months 0-11
    const year = String(date.getFullYear()).slice(-2);
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const filteredCourses = courses
    .filter(
      (c) =>
        c.Title.toLowerCase().includes(search.toLowerCase()) ||
        (c.Instructors &&
          c.Instructors.toLowerCase().includes(search.toLowerCase()))
    )
    .sort((a, b) => {
      if (!sortField) return 0;
      const valA = a[sortField] ?? 0;
      const valB = b[sortField] ?? 0;
      if (typeof valA === "string") {
        return sortOrder === "asc"
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }
      return sortOrder === "asc" ? valA - valB : valB - valA;
    });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Menu Bar */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-6 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-800">Udemy Courses</h1>

            <div className="flex items-center gap-3">
              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg
                  className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                {isLoading ? 'Refreshing...' : 'Refresh'}
              </button>

              {/* Export Dropdown */}
              <div className="relative export-dropdown">
                <button
                  onClick={() => setShowExportMenu(!showExportMenu)}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  Export ({filteredCourses.length})
                  <svg
                    className={`w-4 h-4 transition-transform ${showExportMenu ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {/* Export Menu */}
                {showExportMenu && (
                  <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border z-10">
                    <div className="px-4 py-2 border-b bg-gray-50 text-sm text-gray-600">
                      Exporting {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''}
                    </div>
                    <div className="py-1">
                      <button
                        onClick={() => handleExport('excel')}
                        className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"/>
                        </svg>
                        Export as Excel (.xlsx)
                      </button>
                      <button
                        onClick={() => handleExport('csv')}
                        className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                        </svg>
                        Export as CSV (.csv)
                      </button>
                      <button
                        onClick={() => handleExport('json')}
                        className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd"/>
                        </svg>
                        Export as JSON (.json)
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto p-6">

        <div className="mb-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <input
            type="text"
            placeholder="Search by title or instructor..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="px-4 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-400"
          />
        </div>

        <div className="overflow-x-auto rounded-lg shadow-lg bg-white">
          <table className="min-w-full table-auto border-collapse">
            <thead className="bg-gray-100 text-gray-700 font-semibold">
              <tr>
                {[
                  { display: "Id", field: "Id" },
                  { display: "Title", field: "Title" },
                  { display: "CR", field: "Completion ratio" },
                  { display: "Last accessed time", field: "Last accessed time" },
                  { display: "Instructors", field: "Instructors" }, // will not be sortable
                  { display: "Star Rating", field: "Star Rating" },
                  { display: "Number of Students", field: "Number of Students" },
                  { display: "Course Length", field: "Course Length" },
                ].map((column) =>
                  column.field === "Instructors" ? (
                    <th key={column.field} className="px-4 py-2 border">
                      {column.display}
                    </th>
                  ) : (
                    <th
                      key={column.field}
                      onClick={() => handleSort(column.field)}
                      className="px-4 py-2 border cursor-pointer hover:bg-gray-200 select-none"
                    >
                      {column.display}{" "}
                      {sortField === column.field
                        ? sortOrder === "asc"
                          ? "↑"
                          : "↓"
                        : ""}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {filteredCourses.map((c) => (
                <tr key={c.Id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 border">{c.Id}</td>
                  <td className="px-4 py-2 border">
                    <a
                      href={c.Url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {c.Title}
                    </a>
                  </td>
                  <td className="px-4 py-2 border">
                    {c["Completion ratio"] ?? 0}%
                  </td>
                  <td className="px-4 py-2 border">
                    {formatDate(c["Last accessed time"])}
                  </td>
                  <td className="px-4 py-2 border">{c.Instructors ?? "-"}</td>
                  <td className="px-4 py-2 border flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-[12px] h-[12px]"
                      viewBox="0 0 20 20"
                      fill="gold"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.957a1 1 0 00.95.69h4.153c.969 0 1.371 1.24.588 1.81l-3.36 2.44a1 1 0 00-.364 1.118l1.286 3.957c.3.921-.755 1.688-1.54 1.118l-3.36-2.44a1 1 0 00-1.175 0l-3.36 2.44c-.784.57-1.838-.197-1.539-1.118l1.285-3.957a1 1 0 00-.364-1.118L2.972 9.384c-.783-.57-.38-1.81.588-1.81h4.153a1 1 0 00.95-.69l1.286-3.957z" />
                    </svg>
                    <span className="font-semibold text-gray-800 text-sm">
                      {c["Star Rating"] ? c["Star Rating"].toFixed(1) : "-"}
                    </span>
                  </td>
                  <td className="px-4 py-2 border">
                    {c["Number of Students"] ?? "-"}
                  </td>
                  <td className="px-4 py-2 border">{c["Course Length"] ?? "-"}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredCourses.length === 0 && (
            <div className="p-4 text-center text-gray-500">No courses found.</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
