import pandas as pd

# Load both Excel files
df1 = pd.read_excel("udemy-courses-com.xlsx")
df2 = pd.read_excel("udemy-courses-couk.xlsx")

# Automatically detect ID column
# This assumes the ID column has 'id' in its name (case-insensitive)
def find_id_column(df):
    for col in df.columns:
        if 'id' in col.lower():
            return col
    raise ValueError("No ID column found in the dataframe.")

id_column = find_id_column(df1)  # you can also check df2 if needed

print(f"Detected ID column: {id_column}")

# Merge the files by stacking rows
merged = pd.concat([df1, df2], ignore_index=True)

# Remove duplicate IDs, keeping the first occurrence
merged = merged.drop_duplicates(subset=id_column, keep="first")

# Save to a new Excel file
merged.to_excel("udemy-courses.xlsx", index=False)

print("Merge complete! File saved as 'udemy-courses.xlsx'")

