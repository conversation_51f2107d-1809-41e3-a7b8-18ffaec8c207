@echo off
title Udemy Courses App

:: Check if Windows Terminal is available
where wt >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting Udemy Courses App with Windows Terminal tabs...

    :: Start Windows Terminal with multiple tabs
    wt new-tab --title "Main" cmd /k "echo Udemy Courses App Started & echo. & echo Backend and Frontend are running in separate tabs & echo Close this window to stop all servers & echo. & echo Press Ctrl+C to stop all processes" ^; new-tab --title "Backend" cmd /k "cd /d %~dp0 && echo Starting Backend Server... && uvicorn server:app --reload" ^; new-tab --title "Frontend" cmd /k "cd /d %~dp0udemy-frontend && echo Starting Frontend Server... && npm run dev"

    :: Give servers time to start before opening browser
    timeout /t 8 >nul
    start http://localhost:5173

) else (
    echo Windows Terminal not found, using traditional method...
    echo Starting Udemy Courses App...

    :: Fallback to original method
    :: Start backend (FastAPI) in background
    start "Backend" cmd /k "cd /d %~dp0 && echo Starting Backend Server... && uvicorn server:app --reload"

    :: Start frontend (Vite React) in background
    start "Frontend" cmd /k "cd /d %~dp0udemy-frontend && echo Starting Frontend Server... && npm run dev"

    :: Give frontend a few seconds to boot before opening browser
    timeout /t 8 >nul
    start http://localhost:5173

    echo.
    echo Udemy Courses App running in separate windows!
    echo Close the Backend and Frontend windows to stop the servers.
    echo.
    pause
)
