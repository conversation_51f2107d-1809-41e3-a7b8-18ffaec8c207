@echo off
title Udemy Courses App

echo Starting Udemy Courses App...
echo.

:: Check if Windows Terminal is available
where wt >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Windows Terminal with tabs...

    :: Create temporary batch files to avoid command escaping issues
    echo @echo off > backend_start.bat
    echo cd /d "%~dp0" >> backend_start.bat
    echo echo Starting Backend Server... >> backend_start.bat
    echo uvicorn server:app --reload >> backend_start.bat

    echo @echo off > frontend_start.bat
    echo cd /d "%~dp0udemy-frontend" >> frontend_start.bat
    echo echo Starting Frontend Server... >> frontend_start.bat
    echo npm run dev >> frontend_start.bat

    :: Start Windows Terminal with two tabs using the batch files
    wt new-tab --title "Backend" cmd /k backend_start.bat ; new-tab --title "Frontend" cmd /k frontend_start.bat

    :: Clean up temp files after a short delay
    timeout /t 3 >nul
    del backend_start.bat >nul 2>&1
    del frontend_start.bat >nul 2>&1

) else (
    echo Windows Terminal not found, using separate windows...

    :: Fallback to separate windows
    start "Backend Server - Udemy Courses" cmd /k "cd /d %~dp0 && echo Backend Server Starting... && uvicorn server:app --reload"
    start "Frontend Server - Udemy Courses" cmd /k "cd /d %~dp0udemy-frontend && echo Frontend Server Starting... && npm run dev"
)

:: Wait for servers to start
echo.
echo Waiting for servers to start...
timeout /t 8 >nul

:: Open browser
echo Opening browser...
start http://localhost:5173

echo.
echo ================================
echo Udemy Courses App is running!
echo ================================
echo Backend:  http://127.0.0.1:8000
echo Frontend: http://localhost:5173
echo.
echo Launcher window will close automatically in 3 seconds...
timeout /t 3 >nul
exit
