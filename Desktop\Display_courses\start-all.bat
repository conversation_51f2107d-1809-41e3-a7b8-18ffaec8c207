@echo off
title Udemy Courses App

echo Starting Udemy Courses App...
echo.

:: Check if Windows Terminal is available
where wt >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Windows Terminal with tabs...

    :: Create temporary batch files with full paths
    echo @echo off > "%~dp0backend_start.bat"
    echo cd /d "%~dp0" >> "%~dp0backend_start.bat"
    echo echo Starting Backend Server... >> "%~dp0backend_start.bat"
    echo uvicorn server:app --reload >> "%~dp0backend_start.bat"

    echo @echo off > "%~dp0frontend_start.bat"
    echo cd /d "%~dp0udemy-frontend" >> "%~dp0frontend_start.bat"
    echo echo Starting Frontend Server... >> "%~dp0frontend_start.bat"
    echo npm run dev >> "%~dp0frontend_start.bat"

    :: Start Windows Terminal with two tabs using full paths
    wt new-tab --title "Backend" cmd /k "%~dp0backend_start.bat" ; new-tab --title "Frontend" cmd /k "%~dp0frontend_start.bat"

) else (
    echo Windows Terminal not found, using separate windows...

    :: Fallback to separate windows
    start "Backend Server - Udemy Courses" cmd /k "cd /d %~dp0 && echo Backend Server Starting... && uvicorn server:app --reload"
    start "Frontend Server - Udemy Courses" cmd /k "cd /d %~dp0udemy-frontend && echo Frontend Server Starting... && npm run dev"
)

:: Wait for servers to start
echo.
echo Waiting for servers to start...
timeout /t 8 >nul

:: Clean up temp files now that servers are running
del "%~dp0backend_start.bat" >nul 2>&1
del "%~dp0frontend_start.bat" >nul 2>&1

:: Open browser
echo Opening browser...
start http://localhost:5173

:: Give browser time to open, then minimize only the terminal window
timeout /t 3 >nul
echo Minimizing terminal window...
powershell -Command "Get-Process -Name 'WindowsTerminal' -ErrorAction SilentlyContinue | ForEach-Object { $sig = '[DllImport(\"user32.dll\")] public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);'; Add-Type -MemberDefinition $sig -Name Win32ShowWindow -Namespace Win32Functions; [Win32Functions.Win32ShowWindow]::ShowWindow($_.MainWindowHandle, 2) }" 2>nul

:: Create a PowerShell monitoring script for better reliability
echo Creating browser monitor...
powershell -Command "
$monitorScript = @'
while ($true) {
    Start-Sleep -Seconds 10

    # Check if any browser has localhost:5173 open
    $chromeProcesses = Get-Process -Name 'chrome' -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -like '*localhost:5173*' }
    $edgeProcesses = Get-Process -Name 'msedge' -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -like '*localhost:5173*' }
    $firefoxProcesses = Get-Process -Name 'firefox' -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -like '*localhost:5173*' }

    if (-not $chromeProcesses -and -not $edgeProcesses -and -not $firefoxProcesses) {
        Write-Host 'Browser with localhost:5173 closed, shutting down servers...'

        # Kill backend processes
        Get-Process -Name 'python' -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like '*uvicorn*' } | Stop-Process -Force
        Get-Process -Name 'uvicorn' -ErrorAction SilentlyContinue | Stop-Process -Force

        # Kill frontend processes
        Get-Process -Name 'node' -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like '*vite*' -or $_.CommandLine -like '*npm*' } | Stop-Process -Force

        # Kill Windows Terminal
        Get-Process -Name 'WindowsTerminal' -ErrorAction SilentlyContinue | Stop-Process -Force

        # Clean up this script
        Remove-Item '$PWD\udemy_monitor.ps1' -ErrorAction SilentlyContinue
        break
    }
}
'@
Out-File -FilePath 'udemy_monitor.ps1' -InputObject `$monitorScript -Encoding UTF8
"

:: Start the PowerShell monitor in background
start /min powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "udemy_monitor.ps1"

echo.
echo ================================
echo Udemy Courses App is running!
echo ================================
echo Backend:  http://127.0.0.1:8000
echo Frontend: http://localhost:5173
echo.
echo Terminal window minimized. Check taskbar to access server logs.
echo PowerShell monitor active - servers will auto-shutdown when browser closes.
echo Launcher window will close automatically in 2 seconds...
timeout /t 2 >nul
exit
