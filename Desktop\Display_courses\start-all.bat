@echo off
title Udemy Courses App

echo Starting Udemy Courses App...
echo.

:: Try to use Windows Terminal if available
wt --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Windows Terminal with tabs...

    :: Create a simple batch file for backend
    echo @echo off > temp_backend.bat
    echo title Backend Server >> temp_backend.bat
    echo cd /d "%~dp0" >> temp_backend.bat
    echo echo Starting Backend Server... >> temp_backend.bat
    echo uvicorn server:app --reload >> temp_backend.bat

    :: Create a simple batch file for frontend
    echo @echo off > temp_frontend.bat
    echo title Frontend Server >> temp_frontend.bat
    echo cd /d "%~dp0udemy-frontend" >> temp_frontend.bat
    echo echo Starting Frontend Server... >> temp_frontend.bat
    echo npm run dev >> temp_frontend.bat

    :: Start Windows Terminal with tabs
    start wt new-tab --title "Backend" cmd /k temp_backend.bat ; new-tab --title "Frontend" cmd /k temp_frontend.bat

    :: Clean up temp files after a delay
    timeout /t 2 >nul
    del temp_backend.bat >nul 2>&1
    del temp_frontend.bat >nul 2>&1

) else (
    echo Windows Terminal not available, using separate windows...

    :: Start backend in separate window
    start "Backend Server" cmd /k "cd /d \"%~dp0\" && echo Starting Backend Server... && uvicorn server:app --reload"

    :: Start frontend in separate window
    start "Frontend Server" cmd /k "cd /d \"%~dp0udemy-frontend\" && echo Starting Frontend Server... && npm run dev"
)

:: Wait for servers to start
echo.
echo Waiting for servers to start...
timeout /t 8 >nul

:: Open browser
echo Opening browser...
start http://localhost:5173

echo.
echo ================================
echo Udemy Courses App is running!
echo ================================
echo Backend:  http://127.0.0.1:8000
echo Frontend: http://localhost:5173
echo.
echo Press any key to close this window...
pause >nul
