@echo off
title Udemy Courses App

echo Starting Udemy Courses App...
echo.

:: Simple approach - just use better window titles and keep them organized
echo Starting Backend Server...
start "Backend Server - Udemy Courses" cmd /k "cd /d %~dp0 && echo Backend Server Starting... && echo. && uvicorn server:app --reload"

echo Starting Frontend Server...
start "Frontend Server - Udemy Courses" cmd /k "cd /d %~dp0udemy-frontend && echo Frontend Server Starting... && echo. && npm run dev"

:: Wait for servers to start
echo.
echo Waiting for servers to start...
timeout /t 8 >nul

:: Open browser
echo Opening browser...
start http://localhost:5173

echo.
echo ================================
echo Udemy Courses App is running!
echo ================================
echo Backend:  http://127.0.0.1:8000
echo Frontend: http://localhost:5173
echo.
echo Both servers are running in separate windows.
echo Close both server windows to stop the application.
echo.
echo Press any key to close this launcher window...
pause >nul
