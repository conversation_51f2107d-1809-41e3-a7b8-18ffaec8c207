@echo off
title Udemy Courses App
echo Starting Udemy Courses App...

:: Start backend (FastAPI) in background
start "Backend" cmd /c "cd /d %~dp0 && uvicorn server:app --reload"
:: Save backend process ID
for /f "tokens=2" %%a in ('tasklist /fi "windowtitle eq Backend" /nh') do set BACKEND_PID=%%a

:: Start frontend (Vite React) in background
start "Frontend" cmd /c "cd /d %~dp0udemy-frontend && npm run dev"
:: Save frontend process ID
for /f "tokens=2" %%a in ('tasklist /fi "windowtitle eq Frontend" /nh') do set FRONTEND_PID=%%a

:: Give frontend a few seconds to boot before opening browser
timeout /t 5 >nul
start http://localhost:5173

echo.
echo Udemy Courses App running!
echo Backend PID: %BACKEND_PID%
echo Frontend PID: %FRONTEND_PID%
echo.
echo Close this window to stop both servers.
echo.

:wait
timeout /t 2 >nul
goto wait

:onExit
echo Shutting down servers...
taskkill /PID %BACKEND_PID% /F >nul 2>&1
taskkill /PID %FRONTEND_PID% /F >nul 2>&1
exit
