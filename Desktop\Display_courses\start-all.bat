@echo off
title Udemy Courses App

echo Starting Udemy Courses App...
echo.

:: Check if Windows Terminal is available
where wt >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Windows Terminal with tabs...

    :: Start Windows Terminal with two tabs
    wt new-tab --title "Backend" -d "%~dp0" cmd /k "echo Starting Backend Server... && uvicorn server:app --reload" ; new-tab --title "Frontend" -d "%~dp0udemy-frontend" cmd /k "echo Starting Frontend Server... && npm run dev"

) else (
    echo Windows Terminal not found, using separate windows...

    :: Fallback to separate windows
    start "Backend Server - Udemy Courses" cmd /k "cd /d %~dp0 && echo Backend Server Starting... && uvicorn server:app --reload"
    start "Frontend Server - Udemy Courses" cmd /k "cd /d %~dp0udemy-frontend && echo Frontend Server Starting... && npm run dev"
)

:: Wait for servers to start
echo.
echo Waiting for servers to start...
timeout /t 8 >nul

:: Open browser
echo Opening browser...
start http://localhost:5173

echo.
echo ================================
echo Udemy Courses App is running!
echo ================================
echo Backend:  http://127.0.0.1:8000
echo Frontend: http://localhost:5173
echo.
echo Launcher window will close automatically in 3 seconds...
timeout /t 3 >nul
exit
