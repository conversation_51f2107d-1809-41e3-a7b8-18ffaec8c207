import React, { useEffect, useState } from "react";

export default function CoursesTable() {
  const [courses, setCourses] = useState([]);
  const [search, setSearch] = useState("");

  useEffect(() => {
    fetch("http://127.0.0.1:8000/courses")
      .then((res) => res.json())
      .then((data) => setCourses(data.courses));
  }, []);

  const filtered = courses.filter((c) =>
    c.Title?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Udemy Courses</h1>
      <input
        type="text"
        placeholder="Search courses..."
        className="border p-2 mb-4 rounded w-full"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />
      <div className="overflow-x-auto">
        <table className="min-w-full border rounded shadow">
          <thead className="bg-gray-100">
            <tr>
              <th className="border px-3 py-2">ID</th>
              <th className="border px-3 py-2">Title</th>
              <th className="border px-3 py-2">Completion</th>
              <th className="border px-3 py-2">Last Accessed</th>
              <th className="border px-3 py-2">Enrollment</th>
              <th className="border px-3 py-2">Rating</th>
              <th className="border px-3 py-2">Students</th>
              <th className="border px-3 py-2">Length</th>
            </tr>
          </thead>
          <tbody>
            {filtered.map((c) => (
              <tr key={c.Id} className="hover:bg-gray-50">
                <td className="border px-3 py-2">{c.Id}</td>
                <td className="border px-3 py-2">
                  <a
                    href={c.Url}
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {c.Title}
                  </a>
                </td>
                <td className="border px-3 py-2">{c["Completion ratio"]}%</td>
                <td className="border px-3 py-2">{c["Last accessed time"]}</td>
                <td className="border px-3 py-2">{c["Enrollment time"]}</td>
                <td className="border px-3 py-2">
                  {c["Star Rating"]?.toFixed(2)}
                </td>
                <td className="border px-3 py-2">{c["Number of Students"]}</td>
                <td className="border px-3 py-2">{c["Course Length"]}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
