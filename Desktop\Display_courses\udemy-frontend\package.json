{"name": "udemy-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "backend": "cd .. && uvicorn server:app --reload", "frontend": "vite", "start-all": "concurrently \"npm run backend\" \"npm run frontend\""}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/postcss": "^4.1.12", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.1", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^7.1.2"}}